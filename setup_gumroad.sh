#!/bin/bash

# Gumroad Local Development Setup Script
# This script sets up the complete Gumroad development environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Required versions
REQUIRED_RUBY_VERSION="3.4.3"
REQUIRED_NODE_VERSION="20.17.0"

echo -e "${BLUE}🚀 Starting Gumroad Local Development Setup${NC}"
echo "=================================================="

# Function to print status messages
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to compare versions
version_compare() {
    if [[ $1 == $2 ]]; then
        return 0
    fi
    local IFS=.
    local i ver1=($1) ver2=($2)
    for ((i=${#ver1[@]}; i<${#ver2[@]}; i++)); do
        ver1[i]=0
    done
    for ((i=0; i<${#ver1[@]}; i++)); do
        if [[ -z ${ver2[i]} ]]; then
            ver2[i]=0
        fi
        if ((10#${ver1[i]} > 10#${ver2[i]})); then
            return 1
        fi
        if ((10#${ver1[i]} < 10#${ver2[i]})); then
            return 2
        fi
    done
    return 0
}

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS. Please refer to the README for other platforms."
    exit 1
fi

print_status "Checking system requirements..."

# Check and install Homebrew
if ! command_exists brew; then
    print_warning "Homebrew not found. Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

    # Add Homebrew to PATH for Apple Silicon Macs
    if [[ -f "/opt/homebrew/bin/brew" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
else
    print_success "Homebrew is installed"
fi

# Update Homebrew (ignore errors from broken taps)
print_status "Updating Homebrew..."
brew update || {
    print_warning "Homebrew update had some issues, but continuing..."
    print_status "Attempting to repair broken taps..."
    brew tap --repair || true
}

# Check and install rbenv for Ruby management
if ! command_exists rbenv; then
    print_warning "rbenv not found. Installing rbenv..."
    brew install rbenv ruby-build

    # Add rbenv to shell profile
    if [[ -f ~/.zshrc ]]; then
        echo 'eval "$(rbenv init - zsh)"' >> ~/.zshrc
    elif [[ -f ~/.bash_profile ]]; then
        echo 'eval "$(rbenv init - bash)"' >> ~/.bash_profile
    fi

    # Initialize rbenv for current session
    eval "$(rbenv init -)"
else
    print_success "rbenv is installed"
fi

# Check Ruby version
print_status "Checking Ruby version..."

# Initialize rbenv for current session
eval "$(rbenv init -)"

# Check if the required Ruby version is installed
if rbenv versions | grep -q "$REQUIRED_RUBY_VERSION"; then
    print_success "Ruby $REQUIRED_RUBY_VERSION is already installed"
else
    print_status "Installing Ruby $REQUIRED_RUBY_VERSION..."
    rbenv install $REQUIRED_RUBY_VERSION
fi

# Set the local Ruby version
rbenv local $REQUIRED_RUBY_VERSION
rbenv rehash

# Verify the Ruby version is now correct
CURRENT_RUBY=$(ruby -v | cut -d' ' -f2 | cut -d'p' -f1)
if [[ "$CURRENT_RUBY" == "$REQUIRED_RUBY_VERSION" ]]; then
    print_success "Ruby $REQUIRED_RUBY_VERSION is now active"
else
    print_error "Failed to activate Ruby $REQUIRED_RUBY_VERSION. Current: $CURRENT_RUBY"
    exit 1
fi

# Check Node.js version
print_status "Checking Node.js version..."
if command_exists node; then
    CURRENT_NODE=$(node -v | sed 's/v//')
    version_compare $CURRENT_NODE $REQUIRED_NODE_VERSION
    case $? in
        0) print_success "Node.js $CURRENT_NODE matches required version" ;;
        1) print_success "Node.js $CURRENT_NODE is newer than required $REQUIRED_NODE_VERSION" ;;
        2)
            print_warning "Node.js $CURRENT_NODE is older than required $REQUIRED_NODE_VERSION"
            print_status "Installing Node.js $REQUIRED_NODE_VERSION..."
            brew install node@20
            ;;
    esac
else
    print_warning "Node.js not found. Installing Node.js..."
    brew install node@20
fi

# Check Docker
print_status "Checking Docker..."
if ! command_exists docker; then
    print_error "Docker not found. Please install Docker Desktop from https://www.docker.com/products/docker-desktop"
    print_error "After installing Docker Desktop, restart this script."
    exit 1
fi

# Check if Docker daemon is running
if ! docker ps >/dev/null 2>&1; then
    print_error "Docker daemon is not running. Please start Docker Desktop and try again."
    print_status "Waiting for Docker to start..."

    # Try to start Docker Desktop if it's installed
    if [[ -d "/Applications/Docker.app" ]]; then
        print_status "Attempting to start Docker Desktop..."
        open -a Docker

        # Wait for Docker to start (up to 60 seconds)
        for i in {1..60}; do
            if docker ps >/dev/null 2>&1; then
                print_success "Docker is now running"
                break
            fi
            echo -n "."
            sleep 1
        done
        echo ""

        if ! docker ps >/dev/null 2>&1; then
            print_error "Docker failed to start. Please start Docker Desktop manually and run this script again."
            exit 1
        fi
    else
        print_error "Please start Docker Desktop manually and run this script again."
        exit 1
    fi
else
    print_success "Docker is running"
fi

# Install system dependencies
print_status "Installing system dependencies..."

# MySQL and Percona Toolkit
if ! command_exists mysql; then
    print_status "Installing MySQL..."
    brew install mysql@8.0 percona-toolkit
    brew link --force mysql@8.0

    # Configure MySQL build for Ruby gem
    if command_exists brew && [[ -d "$(brew --prefix openssl)" ]]; then
        bundle config --global build.mysql2 --with-opt-dir="$(brew --prefix openssl)"
    fi

    # Ensure MySQL is not running as a service
    brew services stop mysql@8.0 2>/dev/null || true
else
    print_success "MySQL is installed"
fi

# Image processing libraries
print_status "Installing image processing libraries..."
brew install imagemagick libvips

# FFmpeg for video processing
if ! command_exists ffmpeg; then
    print_status "Installing FFmpeg..."
    brew install ffmpeg
else
    print_success "FFmpeg is installed"
fi

# PDFtk for PDF processing
if ! command_exists pdftk; then
    print_warning "PDFtk not found. You may need to install it manually from:"
    print_warning "https://www.pdflabs.com/tools/pdftk-the-pdf-toolkit/pdftk_server-2.02-mac_osx-10.11-setup.pkg"
fi

# mkcert for SSL certificates
if ! command_exists mkcert; then
    print_status "Installing mkcert..."
    brew install mkcert
else
    print_success "mkcert is installed"
fi

print_success "System dependencies installed successfully!"

# Install Bundler and gems
print_status "Installing Ruby dependencies..."

# Install bundler
if ! command_exists bundle; then
    print_status "Installing Bundler..."
    gem install bundler
else
    print_success "Bundler is installed"
fi

# Install dotenv gem (required for some console commands)
gem install dotenv

# Configure Bundler
print_status "Configuring Bundler..."
bundle config --local without production staging

# Install gems
print_status "Installing Ruby gems (this may take a while)..."
bundle install

# Install Node.js dependencies
print_status "Installing Node.js dependencies..."

# Enable corepack for npm version management
corepack enable

# Install npm dependencies
npm install

# Generate SSL certificates
print_status "Generating SSL certificates..."
if [[ ! -f "config/ssl_certificates/gumroad.dev.crt" ]]; then
    # Install the local CA
    mkcert -install

    # Generate certificates
    bin/generate_ssl_certificates
    print_success "SSL certificates generated"
else
    print_success "SSL certificates already exist"
fi

# Start Docker services
print_status "Starting Docker services..."
print_warning "This will start MySQL, Redis, Elasticsearch, and MongoDB containers..."

# Create necessary directories
mkdir -p docker/tmp/elasticsearch
mkdir -p docker/tmp/mongo
mkdir -p docker/tmp/mysql
mkdir -p docker/tmp/redis

# Start Docker services in the background
COMPOSE_PROJECT_NAME=web docker compose -f docker/docker-compose-local.yml up -d

# Wait for services to be ready
print_status "Waiting for services to start..."
sleep 10

# Check if services are running
print_status "Checking service status..."
if docker ps | grep -q "mysql"; then
    print_success "MySQL container is running"
else
    print_error "MySQL container failed to start"
fi

if docker ps | grep -q "redis"; then
    print_success "Redis container is running"
else
    print_error "Redis container failed to start"
fi

if docker ps | grep -q "elasticsearch"; then
    print_success "Elasticsearch container is running"
else
    print_error "Elasticsearch container failed to start"
fi

# Setup database
print_status "Setting up database..."
bin/rails db:prepare

print_success "Database setup completed!"

# Setup JavaScript assets
print_status "Setting up JavaScript assets..."
npm run setup

# Reset Elasticsearch indices
print_status "Setting up Elasticsearch indices..."
print_warning "This may take a few minutes..."

# Start Rails console and run the reindex command
bin/rails runner "
begin
  DevTools.delete_all_indices_and_reindex_all
  puts 'Elasticsearch indices reset successfully'
rescue => e
  puts 'Warning: Elasticsearch indexing failed: ' + e.message
  puts 'You can run this manually later: DevTools.delete_all_indices_and_reindex_all'
end
"

print_success "Setup completed successfully!"

echo ""
echo "=================================================="
echo -e "${GREEN}🎉 Gumroad is ready to run!${NC}"
echo "=================================================="
echo ""
echo -e "${BLUE}To start the application:${NC}"
echo "  bin/dev"
echo ""
echo -e "${BLUE}The application will be available at:${NC}"
echo "  https://gumroad.dev"
echo ""
echo -e "${BLUE}Default login credentials:${NC}"
echo "  Email: <EMAIL>"
echo "  Password: password"
echo "  2FA Code: 000000"
echo ""
echo -e "${BLUE}To stop Docker services later:${NC}"
echo "  COMPOSE_PROJECT_NAME=web docker compose -f docker/docker-compose-local.yml down"
echo ""

# Ask if user wants to start the application now
read -p "Would you like to start the application now? (y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Starting Gumroad application..."
    echo ""
    echo -e "${GREEN}Starting Rails server, JavaScript build system, and Sidekiq worker...${NC}"
    echo -e "${YELLOW}Press Ctrl+C to stop the application${NC}"
    echo ""

    # Start the application
    # bin/dev
    eval "$(rbenv init -)" && bin/dev
fi
